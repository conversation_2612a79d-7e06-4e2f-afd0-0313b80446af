# We using polars instead of pandas
import polars as pl

# Basket Libraries
from efficient_apriori import apriori, generate_rules_apriori

def clean_data(data):
    if 'InvoiceNumber' in data.columns:
        data['InvoiceNumber'] = data['InvoiceNumber']#if column names have simple adjestments
        data['InvoiceNumber'] = data['InvoiceNumber'].astype(str).str.strip()
    else:
        print("Error: 'InvoiceNumber' column not found in the data.")
        return None
    if 'ProductID' in data.columns:
        data['ProductID'] = data['ProductID'].astype(str).str.strip()
    else:
        print("Error: 'ProductID' column not found in the data.")
        return None
    if 'ProductName' in data.columns:
        data['ProductName'] = data['ProductName'].astype(str).str.strip()
    else:
        print("Error: 'ProductName' column not found in the data.")
        return None
    data.info()

    if 'InvoiceNumber' in data.columns and 'ProductID' in data.columns and 'ProductName' in data.columns:
        data = data.dropna(subset=['InvoiceNumber', 'ProductID','ProductName'])
        data.info()
    # Group by the appropriate columns
    print(f'number of products{data["ProductID"].nunique()}')
    cleaned_data= data.groupby(['InvoiceNumber', 'ProductID','ProductName']).sum().reset_index()
    cleaned_data=cleaned_data[['InvoiceNumber', 'ProductID','ProductName']]
    print(f'number of products{cleaned_data["ProductID"].nunique()}')
    cleaned_data['InvoiceNumber'] = cleaned_data['InvoiceNumber'].astype(str).str.strip()
    cleaned_data.info()
    return cleaned_data


def model_training(df):
    # df['InvoiceNumber'].unique().count()

    df_grouped = (df[['InvoiceNumber','ProductID']].sort(['InvoiceNumber', 'ProductID'])
                .unique(subset=['InvoiceNumber','ProductID'], maintain_order=True)
                .group_by('InvoiceNumber').agg([pl.col('ProductID')])
                )

    transactions = [tuple(row[1]) for row in df_grouped.iter_rows()]
    # transactions[:5]

    min_support = 0.00025
    min_confidence = 0.01
    itemsets, rules = apriori(transactions
                            , min_support=min_support
                            , min_confidence=min_confidence)
    
    # a list of objects with attributes lhs, rhs, lift, confidence, conviction, and support
    filtered_rules = [rule for rule in rules if len(rule.lhs) >= 1 and len(rule.rhs) >= 1]

    # Sort the filtered rules by confidence
    sorted_rules = sorted(filtered_rules, key=lambda rule: rule.confidence)

    # Create a list of dictionaries for the DataFrame
    rules_data = [
        {
            "antecedents": list(rule.lhs),  # Convert lhs to a list
            "consequents": list(rule.rhs),  # Convert rhs to a list
            "lift": rule.lift,
            "confidence": rule.confidence,
            "conviction": rule.conviction,
            "support": rule.support,
        }
        for rule in sorted_rules
    ]

    # Create a DataFrame from the list of dictionaries
    rules_df = pl.DataFrame(rules_data)

    # Sort the DataFrame by confidence in descending order 
    rules_df=rules_df.sort("confidence", descending=True)
    return rules_df

if __name__ == "__main__":
    import pandas as pd
    import os

    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)

    # Load your data - update the path as needed
    data_path = 'path/to/your/monthlySale.csv'  # Update this path
    try:
        data = pd.read_csv(data_path)
        print(f"Loaded data with {len(data)} rows")

        # Clean the data
        cleaned_data = clean_data(data)

        if cleaned_data is not None:
            # Save cleaned data
            cleaned_data.to_csv('data/cleaned_monthlySale.csv', index=False)
            print("Saved cleaned data to data/cleaned_monthlySale.csv")

            # Train model
            association_rules = model_training(cleaned_data)

            # Save model
            import pickle
            with open('data/new_trained_model.pkl', 'wb') as file:
                pickle.dump(association_rules, file)
            print("Saved trained model to data/new_trained_model.pkl")
        else:
            print("Data cleaning failed")
    except FileNotFoundError:
        print(f"Error: Could not find file {data_path}")
























df=pl.read_csv('/content/drive/MyDrive/Colab Notebooks/MARKERT BASKET ANALYSIS/input output_invoice data/monthlySale.csv')
df.head(10)